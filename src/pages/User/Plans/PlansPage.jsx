import React, { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { GlobalContext, showToast } from "Context/Global";

const PlansPage = () => {
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const [subscriptions, setSubscriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState(null);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      
      // Fetch subscriptions from API
      const response = await sdk.GetUserSubscriptions();
      
      if (!response.error) {
        setSubscriptions(response.list);
      } else {
        setError(response.message || "Failed to load subscriptions");
        showToast(globalDispatch, "error", response.message || "Failed to load subscriptions");
      }
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, "error", err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = (subscription) => {
    setSelectedSubscription(subscription);
    setShowCancelModal(true);
  };

  const confirmCancelSubscription = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      
      // Call API to cancel subscription
      const response = await sdk.CancelSubscription({
        subscription_id: selectedSubscription.id.value,
        reason: "User requested cancellation"
      });
      
      if (!response.error) {
        showToast(globalDispatch, "success", "Subscription cancelled successfully");
        setShowCancelModal(false);
        
        // Reload subscriptions to reflect changes
        loadSubscriptionData();
      } else {
        setError(response.message || "Failed to cancel subscription");
        showToast(globalDispatch, "error", response.message || "Failed to cancel subscription");
      }
    } catch (err) {
      setError(err.message);
      showToast(globalDispatch, "error", err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
  };

  const SubscriptionCard = ({ subscription }) => {
    return (
      <div className="mb-6 rounded-lg bg-[#161616] p-6">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-[#eaeaea]">{subscription.name.value}</h2>
          <button
            onClick={() => handleCancelSubscription(subscription)}
            className="rounded-md bg-[#d32f2f] px-4 py-2 text-sm text-white"
          >
            Cancel Subscription
          </button>
        </div>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2H4C2.89543 2 2 2.89543 2 4V12C2 13.1046 2.89543 14 4 14H12C13.1046 14 14 13.1046 14 12V4C14 2.89543 13.1046 2 12 2Z" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 4V8L10.5 10.5" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div>
              <p className="text-xs text-[#b5b5b5]">Start Date</p>
              <p className="text-sm text-[#eaeaea]">{formatDate(subscription.start_date.value)}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 8L14 8" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M10 4L14 8L10 12" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div>
              <p className="text-xs text-[#b5b5b5]">Plan Type</p>
              <p className="text-sm text-[#eaeaea]">{subscription.plan_type.value}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2H4C2.89543 2 2 2.89543 2 4V12C2 13.1046 2.89543 14 4 14H12C13.1046 14 14 13.1046 14 12V4C14 2.89543 13.1046 2 12 2Z" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M11 6H5" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M11 10H5" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div>
              <p className="text-xs text-[#b5b5b5]">Next Billing Date</p>
              <p className="text-sm text-[#eaeaea]">{formatDate(subscription.next_billing_date.value)}</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#1e1e1e] text-[#4caf50]">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                <path d="M8 4.5V8H10.5" stroke="#2E7D32" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div>
              <p className="text-xs text-[#b5b5b5]">Billing Cycle</p>
              <p className="text-sm text-[#eaeaea]">{subscription.billing_cycle.value}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const CancelModal = () => (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
      <div className="w-full max-w-md rounded-lg bg-[#242424] p-6">
        <h3 className="mb-4 text-lg font-semibold text-[#eaeaea]">Cancel Subscription</h3>
        <p className="mb-6 text-sm text-[#b5b5b5]">
          Are you sure you want to cancel your {selectedSubscription?.name.value} subscription? This action cannot be undone.
        </p>
        
        <div className="flex justify-end gap-3">
          <button
            onClick={() => setShowCancelModal(false)}
            className="rounded-lg border border-[#363636] bg-transparent px-4 py-2 text-sm text-[#eaeaea] hover:bg-[#363636]/30"
          >
            No, Keep Subscription
          </button>
          <button
            onClick={confirmCancelSubscription}
            className="rounded-lg bg-[#d32f2f] px-4 py-2 text-sm text-white hover:bg-[#d32f2f]/90"
          >
            Yes, Cancel
          </button>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div className="p-4 text-[#eaeaea]">Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-[#1e1e1e] p-4 md:p-6">
      {error && <Toast message={error} type="error" />}

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-[#eaeaea]">Manage Your Plan</h1>
        <p className="text-[#b5b5b5]">View and manage your subscription details below</p>
      </div>

      {subscriptions.map((subscription) => (
        <SubscriptionCard key={subscription.id.value} subscription={subscription} />
      ))}
      
      <div className="mt-8 text-center text-sm text-[#b5b5b5]">
        Need help? Contact our support team
      </div>

      {showCancelModal && <CancelModal />}
    </div>
  );
};

export default PlansPage; 