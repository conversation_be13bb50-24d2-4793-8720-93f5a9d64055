import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { SkeletonLoader } from "Components/Skeleton";
import { showToast, GlobalContext } from "Context/Global";
import { useContext } from "react";

const PaymentDashboardPage = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const { dispatch: globalDispatch } = useContext(GlobalContext); 
  const [stats, setStats] = useState({
    totalEarned: 0,
    totalPaid: 0,
    pendingPayout: 0,
    availableToWithdraw: 0
  });
  const [activity, setActivity] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [formData, setFormData] = useState({
    card_number: "",
    exp_month: "",
    exp_year: "",
    cvc: "",
    name: "",
    is_default: false
  });
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Format number with commas
  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  // Load all data
  useEffect(() => {
    const loadData = async () => {
      try {
        const sdk = new MkdSDK();
        
        // Fetch all data in parallel
        const [statsData, activityData, methodsData] = await Promise.all([
          sdk.GetEarningsStats(),
          sdk.GetReferralActivity(),
          sdk.GetPaymentMethods()
        ]);

        setStats({
          totalEarned: statsData.model.total_earned.value,
          totalPaid: statsData.model.total_paid.value,
          pendingPayout: statsData.model.pending_payout.value,
          availableToWithdraw: statsData.model.available_to_withdraw.value
        });
        setActivity(activityData.list);
        setPaymentMethods(methodsData.list);
      } catch (error) {
        showToast(globalDispatch, error.message, 5000, "error");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [globalDispatch]);

  const handleWithdraw = async () => {
    if (!withdrawAmount || isNaN(withdrawAmount)) {
      showToast(globalDispatch, "Please enter a valid amount", 5000, "error");
      return;
    }

    try {
      setIsWithdrawing(true);
      const sdk = new MkdSDK();

      // First check if the user's Stripe account is verified
      const verifyResponse = await sdk.callRawAPI(
        "/v1/api/dealmaker/user/stripe/account/verify",
        {},
        "POST"
      );

      if (verifyResponse.complete) {
        // If verified, proceed with transfer
        const transferResponse = await sdk.callRawAPI(
          "/v1/api/dealmaker/user/stripe/transfer",
          {
            amount: parseFloat(withdrawAmount)
          },
          "POST"
        );

        showToast(globalDispatch, "Withdrawal initiated successfully", 5000, "success");
        setWithdrawAmount("");
        
        // Refresh stats
        const statsData = await sdk.GetEarningsStats();
        setStats({
          totalEarned: statsData.model.total_earned.value,
          totalPaid: statsData.model.total_paid.value,
          pendingPayout: statsData.model.pending_payout.value,
          availableToWithdraw: statsData.model.available_to_withdraw.value
        });
      } else {
        // If not verified, get onboarding link and redirect
        const onboardingResponse = await sdk.callRawAPI(
          "/v1/api/dealmaker/user/stripe/onboarding",
          {},
          "POST"
        );
            console.log("onbb",onboardingResponse )
        // Check if the response has the URL in the model property
        if (onboardingResponse && onboardingResponse.url) {
          window.location.href = onboardingResponse.url;
        } else {
          throw new Error("Failed to get Stripe onboarding link");
        }
      }
    } catch (error) {
      console.error("Withdrawal error:", error);
      showToast(globalDispatch, error.message || "Failed to process withdrawal", 5000, "error");
    } finally {
      setIsWithdrawing(false);
    }
  };

  // Payment method icon component
  const PaymentMethodIcon = ({ type }) => {
    if (type === "paypal") {
      return (
        <svg className="h-5 w-5 text-[#7dd87d]" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20.4 9.6H3.6C3.27 9.6 3 9.33 3 9V6C3 5.67 3.27 5.4 3.6 5.4H20.4C20.73 5.4 21 5.67 21 6V9C21 9.33 20.73 9.6 20.4 9.6Z"/>
        </svg>
      );
    }
    return (
      <svg className="h-5 w-5 text-[#7dd87d]" viewBox="0 0 24 24" fill="currentColor">
        <path d="M20 4H4c-1.11 0-1.99.89-1.99 2L2 18c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V6c0-1.11-.89-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/>
      </svg>
    );
  };






  // 3. For the PaymentMethodModal component:
  const PaymentMethodModal = () => {
    // Use local state inside the modal component to prevent parent re-renders
    const [localFormData, setLocalFormData] = useState(formData);
    
    const handleLocalInputChange = (e) => {
      const { name, value, type, checked } = e.target;
      
      // Format card number with spaces
      if (name === "card_number") {
        // Remove non-digit characters
        const digitsOnly = value.replace(/\D/g, '');
        // Format with spaces every 4 digits (maximum 16 digits)
        const formatted = digitsOnly.substring(0, 16).replace(/(\d{4})(?=\d)/g, '$1 ');
        
        setLocalFormData(prev => ({
          ...prev,
          [name]: formatted
        }));
      } 
      // Format expiration month to ensure 2 digits
      else if (name === "exp_month") {
        const monthDigits = value.replace(/\D/g, '').substring(0, 2);
        setLocalFormData(prev => ({
          ...prev,
          [name]: monthDigits
        }));
      }
      // Format expiration year to ensure 4 digits
      else if (name === "exp_year") {
        const yearDigits = value.replace(/\D/g, '').substring(0, 4);
        setLocalFormData(prev => ({
          ...prev,
          [name]: yearDigits
        }));
      }
      // Format CVC to ensure only digits
      else if (name === "cvc") {
        const cvcDigits = value.replace(/\D/g, '').substring(0, 4);
        setLocalFormData(prev => ({
          ...prev,
          [name]: cvcDigits
        }));
      }
      else {
        setLocalFormData(prev => ({
          ...prev,
          [name]: type === "checkbox" ? checked : value
        }));
      }
    };

    // Luhn algorithm for credit card validation
    const validateCardNumber = (number) => {
      const digitsOnly = number.replace(/\s/g, '');
      
      if (!/^\d+$/.test(digitsOnly)) return false;
      
      let sum = 0;
      let shouldDouble = false;
      
      // Loop through values starting from the rightmost digit
      for (let i = digitsOnly.length - 1; i >= 0; i--) {
        let digit = parseInt(digitsOnly.charAt(i));
        
        if (shouldDouble) {
          digit *= 2;
          if (digit > 9) digit -= 9;
        }
        
        sum += digit;
        shouldDouble = !shouldDouble;
      }
      
      return (sum % 10) === 0;
    };

    const validateForm = () => {
      const errors = {};
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-indexed

      // Card number validation
      if (!localFormData.card_number.trim()) {
        errors.card_number = "Card number is required";
      } else {
        const cardNumberDigits = localFormData.card_number.replace(/\s/g, '');
        // Check for valid length (13-19 digits for most cards)
        if (!/^\d{13,19}$/.test(cardNumberDigits)) {
          errors.card_number = "Card number should have 13-19 digits";
        }
        // Validate card number using Luhn algorithm
        else if (!validateCardNumber(cardNumberDigits)) {
          errors.card_number = "Invalid card number";
        }
      }

      // Expiration month validation
      if (!localFormData.exp_month.trim()) {
        errors.exp_month = "Expiration month is required";
      } else if (!/^(0[1-9]|1[0-2])$/.test(localFormData.exp_month)) {
        errors.exp_month = "Invalid month (01-12)";
      }

      // Expiration year validation
      if (!localFormData.exp_year.trim()) {
        errors.exp_year = "Expiration year is required";
      } else if (!/^\d{4}$/.test(localFormData.exp_year)) {
        errors.exp_year = "Invalid year format (YYYY)";
      } else if (parseInt(localFormData.exp_year) < currentYear) {
        errors.exp_year = "Card has expired";
      }

      // Validate expiration date (month and year together)
      if (
        !errors.exp_month &&
        !errors.exp_year &&
        parseInt(localFormData.exp_year) === currentYear &&
        parseInt(localFormData.exp_month) < currentMonth
      ) {
        errors.exp_month = "Card has expired";
      }

      // CVC validation
      if (!localFormData.cvc.trim()) {
        errors.cvc = "CVC is required";
      } else if (!/^\d{3,4}$/.test(localFormData.cvc)) {
        errors.cvc = "Invalid CVC";
      }

      // Cardholder name validation
      if (!localFormData.name.trim()) {
        errors.name = "Cardholder name is required";
      }

      return errors;
    };

    const handleAddPaymentMethod = async (e) => {
      e.preventDefault();
      
      const errors = validateForm();
      setFormErrors(errors);
      
      if (Object.keys(errors).length === 0) {
        setIsSubmitting(true);
        
        try {
          const sdk = new MkdSDK();
          const payload = {
            type: { value: "card" },
            is_default: { value: localFormData.is_default },
            card_number: { value: localFormData.card_number.replace(/\s/g, '') },
            exp_month: { value: localFormData.exp_month },
            exp_year: { value: localFormData.exp_year },
            cvc: { value: localFormData.cvc },
            name: { value: localFormData.name }
          };

          await sdk.AddPaymentMethod(payload);
          showToast(globalDispatch, "Payment method added successfully", 5000, "success");
          
          // Refresh payment methods
          const methodsData = await sdk.GetPaymentMethods();
          setPaymentMethods(methodsData.list);
          
          // Close modal and reset form
          setShowAddPaymentModal(false);
          setFormData({
            card_number: "",
            exp_month: "",
            exp_year: "",
            cvc: "",
            name: "",
            is_default: false
          });
          
        } catch (error) {
          showToast(globalDispatch, error.message, 5000, "error");
        } finally {
          setIsSubmitting(false);
        }
      }
    };
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75">
        <div className="w-[400px] rounded-lg bg-[#161616] p-6">
          {/* Modal header with close button */}
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold text-[#eaeaea]">Add Payment Method</h2>
            <button
              onClick={() => setShowAddPaymentModal(false)}
              className="flex h-8 w-8 items-center justify-center rounded-full bg-[#242424] text-[#b5b5b5] hover:bg-[#363636] hover:text-[#eaeaea] transition-colors"
              type="button"
            >
              <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Payment Type - Card Only */}
          <div className="mb-4">
            <div className="flex items-center gap-2 rounded border border-[#7dd87d] p-2">
              <div className="h-4 w-4 rounded-full border border-[#7dd87d] flex items-center justify-center">
                <div className="h-2 w-2 rounded-full bg-[#7dd87d]" />
              </div>
              <span className="text-sm text-[#eaeaea]">Credit/Debit Card</span>
            </div>
          </div>
          
          {/* Form with local state */}
          <form onSubmit={handleAddPaymentMethod}>
            <div className="mb-3">
              <label className="mb-1 block text-sm text-[#b5b5b5]">Card Number</label>
              <input
                type="text"
                name="card_number"
                value={localFormData.card_number}
                onChange={handleLocalInputChange}
                placeholder="1234 5678 9012 3456"
                className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
              />
              {formErrors.card_number && (
                <p className="mt-1 text-xs text-red-500">{formErrors.card_number}</p>
              )}
            </div>

            <div className="mb-3 flex gap-3">
              <div className="flex-1">
                <label className="mb-1 block text-sm text-[#b5b5b5]">Exp. Month</label>
                <input
                  type="text"
                  name="exp_month"
                  value={localFormData.exp_month}
                  onChange={handleLocalInputChange}
                  placeholder="MM"
                  className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                />
                {formErrors.exp_month && (
                  <p className="mt-1 text-xs text-red-500">{formErrors.exp_month}</p>
                )}
              </div>

              <div className="flex-1">
                <label className="mb-1 block text-sm text-[#b5b5b5]">Exp. Year</label>
                <input
                  type="text"
                  name="exp_year"
                  value={localFormData.exp_year}
                  onChange={handleLocalInputChange}
                  placeholder="YYYY"
                  className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                />
                {formErrors.exp_year && (
                  <p className="mt-1 text-xs text-red-500">{formErrors.exp_year}</p>
                )}
              </div>

              <div className="w-20">
                <label className="mb-1 block text-sm text-[#b5b5b5]">CVC</label>
                <input
                  type="text"
                  name="cvc"
                  value={localFormData.cvc}
                  onChange={handleLocalInputChange}
                  placeholder="123"
                  className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                />
                {formErrors.cvc && (
                  <p className="mt-1 text-xs text-red-500">{formErrors.cvc}</p>
                )}
              </div>
            </div>

            <div className="mb-3">
              <label className="mb-1 block text-sm text-[#b5b5b5]">Cardholder Name</label>
              <input
                type="text"
                name="name"
                value={localFormData.name}
                onChange={handleLocalInputChange}
                placeholder="John Doe"
                className="w-full rounded bg-[#242424] px-3 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
              />
              {formErrors.name && (
                <p className="mt-1 text-xs text-red-500">{formErrors.name}</p>
              )}
            </div>
            
            <div className="mt-4 flex items-center">
              <input
                type="checkbox"
                id="is_default"
                name="is_default"
                checked={localFormData.is_default}
                onChange={handleLocalInputChange}
                className="h-4 w-4 rounded bg-[#242424]"
              />
              <label htmlFor="is_default" className="ml-2 text-sm text-[#b5b5b5]">
                Set as default payment method
              </label>
            </div>
            
            <button
              type="submit"
              disabled={isSubmitting}
              className="mt-6 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50"
            >
              {isSubmitting ? "Adding..." : "Add Payment Method"}
            </button>
          </form>
        </div>
      </div>
    );
  };

  return (
    <div className="p-6">
      {/* Header */}
      <h1 className="text-2xl font-semibold text-[#eaeaea]">Your Earnings</h1>
      <p className="text-sm text-[#b5b5b5]">Track and manage your earnings efficiently</p>

      <div className="mt-6 flex justify-between items-center">
        {/* Total Earned */}
        <div style={{ width: "23%" }} className="rounded-lg bg-[#161616] p-4">
          <div className="flex items-center justify-between">
            <span className="text-xs text-[#b5b5b5]">Total Earned</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="8" height="14" viewBox="0 0 10 16" fill="none">
              <g clip-path="url(#clip0_4_4584)">
                <path d="M4.99998 0C5.55311 0 5.99998 0.446875 5.99998 1V2.11562C6.04998 2.12187 6.09686 2.12812 6.14686 2.1375C6.15936 2.14062 6.16873 2.14062 6.18123 2.14375L7.68123 2.41875C8.22498 2.51875 8.58436 3.04063 8.48436 3.58125C8.38436 4.12187 7.86248 4.48438 7.32186 4.38438L5.83748 4.1125C4.85936 3.96875 3.99686 4.06563 3.39061 4.30625C2.78436 4.54688 2.54061 4.87812 2.48436 5.18437C2.42186 5.51875 2.46873 5.70625 2.52186 5.82188C2.57811 5.94375 2.69373 6.08125 2.92186 6.23438C3.43123 6.56875 4.21248 6.7875 5.22498 7.05625L5.31561 7.08125C6.20936 7.31875 7.30311 7.60625 8.11561 8.1375C8.55936 8.42812 8.97811 8.82187 9.23748 9.37187C9.50311 9.93125 9.55936 10.5563 9.43748 11.2219C9.22186 12.4094 8.40311 13.2031 7.38748 13.6187C6.95936 13.7937 6.49373 13.9062 5.99998 13.9625V15C5.99998 15.5531 5.55311 16 4.99998 16C4.44686 16 3.99998 15.5531 3.99998 15V13.9094C3.98748 13.9062 3.97186 13.9062 3.95936 13.9031H3.95311C3.19061 13.7844 1.93748 13.4563 1.09373 13.0813C0.590609 12.8562 0.362484 12.2656 0.587484 11.7625C0.812484 11.2594 1.40311 11.0312 1.90623 11.2563C2.55936 11.5469 3.63436 11.8344 4.25623 11.9312C5.25311 12.0781 6.07498 11.9937 6.63123 11.7656C7.15936 11.55 7.39998 11.2375 7.46873 10.8625C7.52811 10.5312 7.48123 10.3406 7.42811 10.225C7.36873 10.1 7.25311 9.9625 7.02186 9.80937C6.50936 9.475 5.72498 9.25625 4.70936 8.9875L4.62186 8.96562C3.73123 8.72812 2.63748 8.4375 1.82498 7.90625C1.38123 7.61562 0.965609 7.21875 0.706234 6.66875C0.443734 6.10938 0.390609 5.48438 0.515609 4.81875C0.740609 3.625 1.63436 2.85 2.64998 2.44688C3.06561 2.28125 3.52186 2.16875 3.99998 2.10313V1C3.99998 0.446875 4.44686 0 4.99998 0Z" fill="#7DD87D"/>
              </g>
              <defs>
                <clipPath id="clip0_4_4584">
                  <path d="M0 0H10V16H0V0Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
            <h3 className="mt-1 text-lg font-semibold text-[#eaeaea]">${formatNumber(stats.totalEarned)}</h3>
        </div>

        {/* Total Paid */}
        <div style={{ width: "23%" }} className="rounded-lg bg-[#161616] p-4">
          <div className="flex items-center justify-between">
            <span className="text-xs text-[#b5b5b5]">Total Paid</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 16 16" fill="none">
              <g clip-path="url(#clip0_4_4591)">
                <path d="M8 16C10.1217 16 12.1566 15.1571 13.6569 13.6569C15.1571 12.1566 16 10.1217 16 8C16 5.87827 15.1571 3.84344 13.6569 2.34315C12.1566 0.842855 10.1217 0 8 0C5.87827 0 3.84344 0.842855 2.34315 2.34315C0.842855 3.84344 0 5.87827 0 8C0 10.1217 0.842855 12.1566 2.34315 13.6569C3.84344 15.1571 5.87827 16 8 16ZM11.5312 6.53125L7.53125 10.5312C7.2375 10.825 6.7625 10.825 6.47188 10.5312L4.47188 8.53125C4.17813 8.2375 4.17813 7.7625 4.47188 7.47188C4.76562 7.18125 5.24062 7.17813 5.53125 7.47188L7 8.94063L10.4688 5.46875C10.7625 5.175 11.2375 5.175 11.5281 5.46875C11.8187 5.7625 11.8219 6.2375 11.5281 6.52812L11.5312 6.53125Z" fill="#7DD87D"/>
              </g>
              <defs>
                <clipPath id="clip0_4_4591">
                  <path d="M0 0H16V16H0V0Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
            <h3 className="mt-1 text-lg font-semibold text-[#eaeaea]">${formatNumber(stats.totalPaid)}</h3>
        </div>

        {/* Pending Payout */}
        <div style={{ width: "23%" }} className="rounded-lg bg-[#161616] p-4">
          <div className="flex items-center justify-between">
            <span className="text-xs text-[#b5b5b5]">Pending Payout</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 16 16" fill="none">
              <g clip-path="url(#clip0_4_4598)">
                <path d="M8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0ZM7.25 3.75V8C7.25 8.25 7.375 8.48438 7.58437 8.625L10.5844 10.625C10.9281 10.8562 11.3938 10.7625 11.625 10.4156C11.8562 10.0687 11.7625 9.60625 11.4156 9.375L8.75 7.6V3.75C8.75 3.33437 8.41562 3 8 3C7.58437 3 7.25 3.33437 7.25 3.75Z" fill="#FFD700"/>
              </g>
              <defs>
                <clipPath id="clip0_4_4598">
                  <path d="M0 0H16V16H0V0Z" fill="white"/>
                </clipPath>
              </defs>
            </svg>
          </div>
          <h3 className="mt-1 text-lg font-semibold text-[#eaeaea]">${formatNumber(stats.pendingPayout)}</h3>
        </div>

        {/* Available to Withdraw */}
        <div style={{ width: "23%" }} className="rounded-lg bg-[#161616] p-4">
          <div className="flex items-center justify-between">
            <span className="text-xs text-[#b5b5b5]">Available to Withdraw</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 16 16" fill="none">
              <path d="M0 0H16V16H0V0Z" stroke="#E5E7EB"/>
              <path d="M2 1C0.896875 1 0 1.89688 0 3V13C0 14.1031 0.896875 15 2 15H14C15.1031 15 16 14.1031 16 13V6C16 4.89687 15.1031 4 14 4H2.5C2.225 4 2 3.775 2 3.5C2 3.225 2.225 3 2.5 3H14C14.5531 3 15 2.55313 15 2C15 1.44687 14.5531 1 14 1H2ZM13 8.5C13.2652 8.5 13.5196 8.60536 13.7071 8.79289C13.8946 8.98043 14 9.23478 14 9.5C14 9.76522 13.8946 10.0196 13.7071 10.2071C13.5196 10.3946 13.2652 10.5 13 10.5C12.7348 10.5 12.4804 10.3946 12.2929 10.2071C12.1054 10.0196 12 9.76522 12 9.5C12 9.23478 12.1054 8.98043 12.2929 8.79289C12.4804 8.60536 12.7348 8.5 13 8.5Z" fill="#7DD87D"/>
            </svg>
          </div>
          <h3 className="mt-1 text-lg font-semibold text-[#eaeaea]">${formatNumber(stats.availableToWithdraw)}</h3>
        </div>
      </div>

      <div className="mt-8 flex gap-6">
        {/* Referral Activity - Left Column */}
        <div className="flex-1 rounded-lg bg-[#161616] p-4">
          <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">Activity</h2>
          <table className="w-full">
            <thead>
              <tr className="text-left text-sm text-[#b5b5b5]">
                <th className="pb-2">Date</th>
                <th className="pb-2">Activity</th>
                <th className="pb-2">Status</th>
                <th className="pb-2 text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
            {activity.map((item, index) => (
                <tr key={`${item.id.value}-${item.date.value}-${index}`}>
                  <td className="py-2 text-sm text-[#eaeaea]">
                    {new Date(item.date.value).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}
                  </td>
                  <td className="py-2 text-sm text-[#eaeaea]">{item.referral.value.split('_').join(' ')}</td>
                  <td className="py-2">
                    <span style={{
                      color: item.status.value === "completed" ? "#7dd87d" : "#ffd700"
                    }} className={`rounded px-2 py-1 text-xs`}>
                      {item.status.value.charAt(0).toUpperCase() + item.status.value.slice(1)}
                    </span>
                  </td>
                  <td className="py-2 text-right text-sm text-[#eaeaea]">
                    ${formatNumber(item.amount.value)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Right Column */}
        <div className="w-[400px] space-y-6">
          {/* Withdraw Funds */}
          <div className="rounded-lg bg-[#161616] p-4">
            <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">Withdraw Funds</h2>
            <div className="relative">
              <span style={{
                top: "50%",
                left: "10px",
                transform: "translateY(-50%)"
              }} className="absolute text-[#eaeaea]">$</span>
              <input 
                type="text"
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                placeholder="Enter amount"
                className="w-full rounded bg-[#242424] px-8 py-2 text-[#eaeaea] placeholder-[#b5b5b5]"
                disabled={isWithdrawing}
              />
            </div>
            <button 
              onClick={handleWithdraw}
              disabled={isWithdrawing}
              className="mt-4 w-full rounded bg-[#2e7d32] py-2 text-sm text-[#eaeaea] disabled:opacity-50"
            >
              {isWithdrawing ? "Processing..." : "Withdraw Funds"}
            </button>
          </div>

          {/* Payment Methods */}
          <div className="rounded-lg bg-[#161616] p-4 mt-4">
            <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">Payment Methods</h2>
            {paymentMethods.map((method) => (
              <div key={method.id.value} className="mb-3 flex items-center justify-between rounded bg-[#242424] p-3">
                <div className="flex items-center gap-2">
                  <PaymentMethodIcon type={method.type.value} />
                  <span className="text-sm text-[#eaeaea]">
                    {method.type.value === "card" ? "Card (Ending in " + method.last4.value + ")" : "PayPal"}
                  </span>
                </div>
                {method.is_default.value && (
                  <span className="text-xs text-[#7dd87d]">Default</span>
                )}
              </div>
            ))}
            <button 
              style={{
                border: "1px solid #7dd87d"
              }} 
              className="mt-4 flex w-full items-center justify-center gap-2 rounded bg-[#242424] py-2 text-sm text-[#7dd87d]"
              onClick={() => setShowAddPaymentModal(true)}
            >
              <svg 
                style={{
                  color: "#7dd87d"
                }} 
                className="h-4 w-4" 
                fill="currentColor" 
                viewBox="0 0 24 24"
              >
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
              </svg>
              Add Payment Method
            </button>
          </div>
        </div>
      </div>

      {showAddPaymentModal && <PaymentMethodModal />}
    </div>
  );
};

export default PaymentDashboardPage;
