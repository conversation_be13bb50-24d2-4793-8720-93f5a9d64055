import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import MkdSDK from "Utils/MkdSDK";
import { Toast } from "Components/Toast";
import { FaGoogle, FaMicrosoft } from "react-icons/fa";
import { SkeletonLoader } from "Components/Skeleton";
import { GlobalContext, showToast } from "Context/Global";
import { useContext } from "react";
import { Modal } from "Components/Modal/Modal";

// Import logos from MeetingsPage
const GoogleMeetLogo = () => (
  <div className="flex justify-center items-center w-10 h-10 bg-white rounded-lg">
    <svg
      width="20"
      height="20"
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M13.979 10.5V14.5L17.979 17V8L13.979 10.5Z" fill="#00832D" />
      <path
        d="M4.979 8C4.979 7.46957 5.1897 6.96086 5.56478 6.58579C5.93985 6.21071 6.44857 6 6.979 6H12.979C13.5094 6 14.0181 6.21071 14.3932 6.58579C14.7683 6.96086 14.979 7.46957 14.979 8V16C14.979 16.5304 14.7683 17.0391 14.3932 17.4142C14.0181 17.7893 13.5094 18 12.979 18H6.979C6.44857 18 5.93985 17.7893 5.56478 17.4142C5.1897 17.0391 4.979 16.5304 4.979 16V8Z"
        fill="#00832D"
      />
      <path
        d="M20.479 6.98C20.479 6.574 20.16 6.214 19.765 6.162L6.978 6.165C6.448 6.165 5.939 6.375 5.564 6.75C5.189 7.125 4.979 7.634 4.979 8.164V15.836C4.979 16.366 5.189 16.875 5.564 17.25C5.939 17.625 6.448 17.835 6.978 17.835H12.979C13.509 17.835 14.018 17.625 14.393 17.25C14.768 16.875 14.978 16.366 14.978 15.836V10.5L20.479 16V6.98Z"
        fill="#00AC47"
      />
      <path
        d="M20.479 6.98V16L17.979 13.5V8.5L19.765 6.162C20.16 6.214 20.479 6.574 20.479 6.98Z"
        fill="#0066DA"
      />
      <path
        d="M13.979 10.5L14.978 10.5V15.836C14.978 16.366 14.768 16.875 14.393 17.25C14.018 17.625 13.509 17.835 12.979 17.835H6.978C6.448 17.835 5.939 17.625 5.564 17.25C5.189 16.875 4.979 16.366 4.979 15.836V8.164C4.979 7.634 5.189 7.125 5.564 6.75C5.939 6.375 6.448 6.165 6.978 6.165L13.979 6.165V10.5Z"
        fill="#00AC47"
      />
      <path
        d="M19.765 6.162L17.979 8.5V13.5L20.479 16V6.98C20.479 6.574 20.16 6.214 19.765 6.162Z"
        fill="#2684FC"
      />
      <path
        d="M14.978 10.5H13.979V6.165H6.978L6.979 6.165C6.449 6.165 5.939 6.375 5.564 6.75C5.189 7.125 4.979 7.634 4.979 8.164V15.836C4.979 16.366 5.189 16.875 5.564 17.25C5.939 17.625 6.448 17.835 6.978 17.835H12.979C13.509 17.835 14.018 17.625 14.393 17.25C14.768 16.875 14.978 16.366 14.978 15.836V10.5Z"
        fill="#00AC47"
      />
    </svg>
  </div>
);

const ZoomLogo = () => (
  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#4A8CFF] text-white">
    <svg
      width="20"
      height="20"
      viewBox="0 0 2499 2500"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2499 1253C2499 1942.5 1942 2500 1250 2500C559.5 2500 0 1942.5 0 1253C0 563.5 559.5 0 1250 0C1942 0 2499 563.5 2499 1253Z"
        fill="#4A8CFF"
      />
      <path d="M534 1090V1829.5H1254.5V1939H424V1090H534Z" fill="white" />
      <path d="M1044.5 561H2075V1410.5H1964.5V670.5H1044.5V561Z" fill="white" />
      <path d="M753 895.5H1746.5V1595H753V895.5Z" fill="white" />
    </svg>
  </div>
);

const OutlookLogo = () => (
  <div className="flex justify-center items-center w-10 h-10 bg-white rounded-lg">
    <FaMicrosoft className="h-6 w-6 text-[#0078d4]" />
  </div>
);

const HighLevelLogo = () => (
  <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-[#363636] text-white">
    <span className="font-bold text-md">H</span>
  </div>
);

const MSTeamsLogo = () => (
  <div className="flex justify-center items-center w-10 h-10 bg-white rounded-lg">
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.707 7.707H13.707V4.293C13.707 3.58 13.127 3 12.414 3H4.293C3.58 3 3 3.58 3 4.293V12.414C3 13.127 3.58 13.707 4.293 13.707H7.707V16.414C7.707 17.127 8.287 17.707 9 17.707H17.121C17.834 17.707 18.414 17.127 18.414 16.414V8.414C18.414 8.066 18.269 7.731 18.012 7.475C17.756 7.218 17.421 7.073 17.073 7.073L17.707 7.707Z"
        fill="#5059C9"
      />
      <path
        d="M17.707 7.707H13.707V4.293C13.707 3.58 13.127 3 12.414 3H4.293C3.58 3 3 3.58 3 4.293V12.414C3 13.127 3.58 13.707 4.293 13.707H7.707V16.414C7.707 17.127 8.287 17.707 9 17.707H17.121C17.834 17.707 18.414 17.127 18.414 16.414V8.414C18.414 8.066 18.269 7.731 18.012 7.475C17.756 7.218 17.421 7.073 17.073 7.073L17.707 7.707Z"
        fill="url(#paint0_linear)"
      />
      <path
        d="M3 12.415V10.708H13.708V17.708H9C9 17.708 7.708 17.708 7.708 16.415C7.708 15.122 7.708 13.829 7.708 13.829C7.708 13.829 7.708 13.708 7.586 13.708H4.293C4.293 13.708 3 13.708 3 12.415Z"
        fill="#7B83EB"
      />
      <path opacity="0.1" d="M5.585 6H13.707V8.293H5.585V6Z" fill="black" />
      <path opacity="0.2" d="M5 6.585H13.707V8.293H5V6.585Z" fill="black" />
      <path opacity="0.2" d="M5 6.585H13.122V7.707H5V6.585Z" fill="black" />
      <path opacity="0.2" d="M5 6.585H12.537V7.122H5V6.585Z" fill="black" />
      <path
        d="M12.415 3H4.293C3.58 3 3 3.58 3 4.293V12.415C3 13.128 3.58 13.708 4.293 13.708H12.415C13.128 13.708 13.708 13.128 13.708 12.415V4.293C13.708 3.58 13.128 3 12.415 3Z"
        fill="url(#paint1_linear)"
      />
      <path
        d="M10.122 6C10.9801 6 11.674 6.69396 11.674 7.552C11.674 8.41004 10.9801 9.104 10.122 9.104C9.26398 9.104 8.57002 8.41004 8.57002 7.552C8.57002 6.69396 9.26398 6 10.122 6Z"
        fill="white"
      />
      <path
        d="M12.171 11.4118H8.07304C8.07304 10.3304 8.94767 9.45581 10.0291 9.45581H10.2153C11.2967 9.45581 12.1713 10.3304 12.1713 11.4118H12.171Z"
        fill="white"
      />
      <path d="M17.122 8.293H14.293V11.122H17.122V8.293Z" fill="#5059C9" />
      <path
        d="M14.293 11.121H17.122V12.414C17.122 13.127 16.542 13.707 15.829 13.707H14.293V11.121Z"
        fill="#5059C9"
      />
      <defs>
        <linearGradient
          id="paint0_linear"
          x1="10.707"
          y1="7.707"
          x2="10.707"
          y2="17.707"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A62C3" />
          <stop offset="1" stopColor="#4D55BD" />
        </linearGradient>
        <linearGradient
          id="paint1_linear"
          x1="8.354"
          y1="3"
          x2="8.354"
          y2="13.708"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5A62C3" />
          <stop offset="1" stopColor="#4D55BD" />
        </linearGradient>
      </defs>
    </svg>
  </div>
);

const GoogleCalendarLogo = () => (
  <div className="flex justify-center items-center w-10 h-10 bg-white rounded-lg">
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M5 5H19V19H5V5Z" fill="white" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.778 4H4.222C3 4 2 5 2 6.222V19.778C2 21 3 22 4.222 22H19.778C21 22 22 21 22 19.778V6.222C22 5 21 4 19.778 4ZM19.778 19.778H4.222V9.611H19.778V19.778ZM4.222 6.222V7.333H19.778V6.222H4.222Z"
        fill="#4285F4"
      />
      <path
        d="M12.4444 14.2222H15.6667V17.4444H12.4444V14.2222Z"
        fill="#4285F4"
      />
    </svg>
  </div>
);

const OutlookCalendarLogo = () => (
  <div className="flex justify-center items-center w-10 h-10 bg-white rounded-lg">
    <svg
      width="20"
      height="20"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M13.2 12L24 16.8V7.2L13.2 12Z" fill="#0078D4" />
      <path d="M12 11.4L0 6V18L12 12.6V11.4Z" fill="#0078D4" />
      <path
        d="M0 6L12 12.6L24 7.2V6C24 4.3 22.7 3 21 3H3C1.3 3 0 4.3 0 6Z"
        fill="#50D9FF"
      />
      <path
        d="M0 18C0 19.7 1.3 21 3 21H21C22.7 21 24 19.7 24 18V16.8L12 11.4L0 18Z"
        fill="#103A9E"
      />
    </svg>
  </div>
);

const IntegrationsPage = () => {
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(true);
  const [userIntegrations, setUserIntegrations] = useState([]);
  const [fetching, setFetching] = useState(false);
  const [found, setFound] = useState(false);
  const [connecting, setConnecting] = useState(false);
  const [showGHLForm, setShowGHLForm] = useState(false);
  const [ghlForm, setGHLForm] = useState({
    calendar_id: "",
    timezone: "America/New_York",
    api_key: "",
  });
  const navigate = useNavigate();
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const sdk = new MkdSDK();

  useEffect(() => {
    let isSubscribed = true;

    const handleCallback = async () => {
      if (found || connecting) {
        return;
      }

      const sdk = new MkdSDK();
      const params = sdk.ParseIntegrationCallback();

      // If no callback parameters are present, return early
      if (!params.type || !params.code) {
        return;
      }

      try {
        setFound(true);
        setConnecting(true);

        let response;
        if (params.type === "zoom") {
          response = await sdk.ZoomCallback(params);
        } else if (params.type === "google") {
          response = await sdk.GoogleCallback(params);
        } else if (params.type === "outlook") {
          response = await sdk.callRawAPI(
            "/v1/api/dealmaker/integrations/outlook/callback?code=" +
              params.code +
              "&state=" +
              params.state,
            {},
            "GET"
          );
        }

        if (!isSubscribed) return;

        if (!response.error) {
          navigate("/member/integrations");
        } else {
          navigate("/member/integrations?error=" + response.message);
          showToast(globalDispatch, response.message, 5000, "error");
        }
      } catch (err) {
        if (!isSubscribed) return;
        console.error("Callback error:", err);
        navigate(
          "/member/integrations?error=" + (err.message || "Unknown error")
        );
        showToast(
          globalDispatch,
          err.message || "Unknown error",
          5000,
          "error"
        );
      }
    };

    handleCallback();

    return () => {
      isSubscribed = false;
    };
  }, [navigate, globalDispatch]); // Remove found and connecting from dependencies

  // Service definitions with their logos and availability
  const availableServices = {
    calendar_services: [
      {
        id: "google_meet",
        name: "Google Calendar",
        description: "Sync your Google Calendar events",
        icon: GoogleCalendarLogo,
        available: true,
      },
      {
        id: "outlook",
        name: "Outlook Calendar",
        description: "Sync your Outlook Calendar events",
        icon: OutlookCalendarLogo,
        available: true,
      },
      {
        id: "ghl_calendar",
        name: "HighLevel",
        description: "Sync your HighLevel calendar",
        icon: HighLevelLogo,
        available: true,
      },
    ],
    meeting_platforms: [
      {
        id: "google_meet",
        name: "Google Meet",
        description: "Connect with Google Meet",
        icon: GoogleMeetLogo,
        available: true,
      },
      {
        id: "zoom",
        name: "Zoom",
        description: "Connect with Zoom",
        icon: ZoomLogo,
        available: true,
      },
      {
        id: "outlook",
        name: "Microsoft Teams",
        description: "Connect with Microsoft Teams",
        icon: MSTeamsLogo,
        available: true,
      },
    ],
  };

  // Generate display data from available services and user integrations
  const [calendarServices, setCalendarServices] = useState([]);
  const [meetingPlatforms, setMeetingPlatforms] = useState([]);

  useEffect(() => {
    loadUserIntegrations();
  }, []);

  // Process services with user integration status
  useEffect(() => {
    processServices();
  }, [userIntegrations]);

  const loadUserIntegrations = async () => {
    try {
      setLoading(true);
      const sdk = new MkdSDK();
      const response = await sdk.GetUserIntegrations();

      if (!response.error) {
        setUserIntegrations(response.data || []);
      }
    } catch (err) {
      setError(err.message || "Failed to load integrations");
      showToast(
        globalDispatch,
        "error",
        err.message || "Failed to load integrations"
      );
    } finally {
      setLoading(false);
    }
  };

  const processServices = () => {
    // Map available services to their connected status
    const mapServiceWithConnections = (service) => {
      const connectedIntegration = userIntegrations.find((integration) => {
        return integration.service_id === service.id;
      });
      // console.log(service, connectedIntegration);

      return {
        id: { value: service.id },
        name: { value: service.name },
        description: { value: service.description },
        status: {
          value: connectedIntegration
            ? "connected"
            : service.available
            ? "available"
            : "unavailable",
        },
        integration_id: connectedIntegration?.id,
        icon: service.icon,
        action: connectedIntegration
          ? "Connected"
          : service.available
          ? "Connect"
          : "Not Available",
      };
    };

    // Process calendar services
    const calendars = availableServices.calendar_services.map(
      mapServiceWithConnections
    );
    console.log(calendars);
    setCalendarServices(calendars);

    // Process meeting platforms
    const platforms = availableServices.meeting_platforms.map(
      mapServiceWithConnections
    );
    console.log(platforms);
    setMeetingPlatforms(platforms);
  };

  const handleConnect = async (serviceId) => {
    console.log(serviceId);
    try {
      const sdk = new MkdSDK();

      if (serviceId === "google_calendar" || serviceId === "google_meet") {
        const response = await sdk.GetGoogleAuthUrl();
        if (!response.error && response.auth_url) {
          console.log(response.auth_url);
          window.location.href = response.auth_url;
        }
      } else if (serviceId === "zoom") {
        console.log("zoom");
        const response = await sdk.GetZoomAuthUrl();
        if (!response.error && response.auth_url) {
          console.log(response.auth_url, "the auth url");
          window.location.href = response.auth_url;
        }
      } else if (serviceId === "outlook") {
        const response = await sdk.callRawAPI(
          "/v1/api/dealmaker/user/integrations/outlook/auth-url",
          {},
          "GET"
        );
        if (!response.error && response.auth_url) {
          window.location.href = response.auth_url;
        }
      } else if (serviceId === "ghl_calendar") {
        setShowGHLForm(true);
      }
    } catch (err) {
      setError(err.message || "Failed to connect service");
      showToast(
        globalDispatch,
        "error",
        err.message || "Failed to connect service"
      );
    }
  };

  const handleInputChange = (field, value) => {
    setGHLForm((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleGHLSubmit = async () => {
    try {
      const sdk = new MkdSDK();
      const response = await sdk.ConnectGHLCalendar({
        calendar_id: ghlForm.calendar_id,
        timezone: ghlForm.timezone,
        api_key: ghlForm.api_key,
      });

      if (!response.error) {
        showToast(
          globalDispatch,
          "success",
          "GHL Calendar connected successfully"
        );
        setShowGHLForm(false);
        setGHLForm({
          calendar_id: "",
          timezone: "America/New_York",
          api_key: "",
        });

        // Update the services list to show GHL as connected
        const updatedCalendarServices = calendarServices.map((service) => {
          if (service.id.value === "ghl_calendar") {
            return {
              ...service,
              status: { value: "connected" },
              action: "Connected",
              integration_id: response.data?.id, // Store the integration ID if available
            };
          }
          return service;
        });
        setCalendarServices(updatedCalendarServices);

        // Also refresh integrations to get the latest state
        await loadUserIntegrations();
      }
    } catch (err) {
      setError(err.message || "Failed to connect GHL calendar");
      showToast(
        globalDispatch,
        "error",
        err.message || "Failed to connect GHL calendar"
      );
    }
  };

  const handleDisconnect = async (integrationId) => {
    try {
      const sdk = new MkdSDK();
      await sdk.DisconnectIntegration(integrationId);

      // Refresh integrations
      await loadUserIntegrations();
      showToast(
        globalDispatch,
        "success",
        "Integration disconnected successfully"
      );
    } catch (err) {
      setError(err.message || "Failed to disconnect service");
      showToast(
        globalDispatch,
        "error",
        err.message || "Failed to disconnect service"
      );
    }
  };

  const IntegrationCard = ({ service }) => (
    <div className="rounded-lg bg-[#242424] p-6">
      <div className="flex gap-3 items-center mb-4">
        <service.icon />
        <h3 className="text-lg font-medium text-[#eaeaea]">
          {service.name.value}
        </h3>
      </div>
      <p className="mb-6 text-sm text-[#b5b5b5]">{service.description.value}</p>
      <button
        onClick={() =>
          service.status.value === "connected"
            ? handleDisconnect(service.integration_id)
            : handleConnect(service.id.value)
        }
        disabled={service.status.value === "unavailable"}
        className={`w-full rounded-lg px-4 py-2 text-sm ${
          service.status.value === "connected"
            ? "bg-[#2e7d32]/20 text-[#7dd87d]"
            : service.status.value === "unavailable"
            ? "cursor-not-allowed bg-[#363636] text-[#b5b5b5]"
            : "bg-[#2e7d32] text-[#eaeaea] hover:bg-[#2e7d32]/90"
        }`}
      >
        {service.action}
      </button>
    </div>
  );

  const renderSkeletons = () => (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(3)].map((_, i) => (
        <SkeletonLoader key={i} className="w-full h-48 rounded-lg" />
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {error && <Toast message={error} type="error" />}

      {/* Header */}
      <div className="mb-8">
        <h2 className="text-xl font-bold text-[#eaeaea]">Integrations</h2>
        <p className="text-[#b5b5b5]">
          Connect your calendars and platforms to manage meetings effortlessly
        </p>
      </div>

      {/* GHL Form */}
      {showGHLForm && (
        <div className="flex fixed inset-0 z-50 justify-center items-center p-4 backdrop-blur-sm">
          <div
            className="fixed inset-0 bg-black/30"
            onClick={() => setShowGHLForm(false)}
          />

          <div className="relative w-full max-w-md rounded-xl bg-[#242424] p-6 shadow-2xl ring-1 ring-white/10">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-medium text-[#eaeaea]">
                Connect GHL Calendar
              </h3>
              <button
                onClick={() => setShowGHLForm(false)}
                className="rounded-lg p-1 text-[#b5b5b5] hover:bg-white/5 hover:text-[#eaeaea]"
              >
                <svg
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>

            <div className="space-y-5">
              <div>
                <label className="mb-2 block text-sm font-medium text-[#b5b5b5]">
                  Calendar ID
                </label>
                <input
                  type="text"
                  value={ghlForm.calendar_id}
                  onChange={(e) =>
                    handleInputChange("calendar_id", e.target.value)
                  }
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]"
                  placeholder="Enter your GHL Calendar ID"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-[#b5b5b5]">
                  API Key
                </label>
                <input
                  type="password"
                  value={ghlForm.api_key}
                  onChange={(e) => handleInputChange("api_key", e.target.value)}
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]"
                  placeholder="Enter your GHL API Key"
                />
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-[#b5b5b5]">
                  Timezone
                </label>
                <input
                  type="text"
                  value={ghlForm.timezone}
                  onChange={(e) =>
                    handleInputChange("timezone", e.target.value)
                  }
                  className="w-full rounded-lg border border-[#363636] bg-[#1e1e1e] px-4 py-2.5 text-[#eaeaea] placeholder-[#666] transition-colors focus:border-[#2e7d32] focus:outline-none focus:ring-1 focus:ring-[#2e7d32]"
                  placeholder="e.g. America/New_York"
                />
              </div>

              <div className="flex justify-end gap-3 border-t border-[#363636] pt-6">
                <button
                  onClick={() => setShowGHLForm(false)}
                  className="px-4 py-2 text-sm font-medium text-[#b5b5b5] transition-colors hover:text-[#eaeaea]"
                >
                  Cancel
                </button>
                <button
                  onClick={handleGHLSubmit}
                  className="rounded-lg bg-[#2e7d32] px-4 py-2 text-sm font-medium text-[#eaeaea] transition-colors hover:bg-[#2e7d32]/90 focus:outline-none focus:ring-2 focus:ring-[#2e7d32] focus:ring-offset-2 focus:ring-offset-[#242424]"
                >
                  Connect
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar Services */}
      <div className="mb-8">
        <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">
          Calendar Services
        </h2>
        {loading ? (
          renderSkeletons()
        ) : (
          <div className="grid grid-cols-3 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {calendarServices.map((service) => (
              <IntegrationCard key={service.id.value} service={service} />
            ))}
          </div>
        )}
      </div>

      {/* Meeting Platforms */}
      <div>
        <h2 className="mb-4 text-lg font-semibold text-[#eaeaea]">
          Meeting Platforms
        </h2>
        {loading ? (
          renderSkeletons()
        ) : (
          <div className="grid grid-cols-3 gap-4 md:grid-cols-2 lg:grid-cols-3">
            {meetingPlatforms.map((service) => (
              <IntegrationCard key={service.id.value} service={service} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default IntegrationsPage;
