import { useNavigate, useLocation } from "react-router-dom";
import { AuthContext } from "Context/Auth";
import { GlobalContext } from "Context/Global";
import { useContext } from "react";

const Sidebar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dispatch } = useContext(AuthContext);
  const { dispatch: globalDispatch } = useContext(GlobalContext);

  const handleLogout = () => {
    // Clear tokens
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("refreshToken");
    dispatch({
      type: "LOGOUT",
      payload: {
        user: null,
        token: null,
        role: null
      }
    });
    navigate("/member/login");
  };

  return (
    <div className="flex h-screen w-64 flex-col bg-black p-4">
      <div className="flex-1 space-y-2">


        <button
          onClick={() => navigate("/member/dashboard")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/dashboard")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_542)">
              <path d="M0 8C0 5.87827 0.842855 3.84344 2.34315 2.34315C3.84344 0.842855 5.87827 0 8 0C10.1217 0 12.1566 0.842855 13.6569 2.34315C15.1571 3.84344 16 5.87827 16 8C16 10.1217 15.1571 12.1566 13.6569 13.6569C12.1566 15.1571 10.1217 16 8 16C5.87827 16 3.84344 15.1571 2.34315 13.6569C0.842855 12.1566 0 10.1217 0 8ZM9 3C9 2.73478 8.89464 2.48043 8.70711 2.29289C8.51957 2.10536 8.26522 2 8 2C7.73478 2 7.48043 2.10536 7.29289 2.29289C7.10536 2.48043 7 2.73478 7 3C7 3.26522 7.10536 3.51957 7.29289 3.70711C7.48043 3.89464 7.73478 4 8 4C8.26522 4 8.51957 3.89464 8.70711 3.70711C8.89464 3.51957 9 3.26522 9 3ZM8 13C9.10312 13 10 12.1031 10 11C10 10.4563 9.78438 9.96562 9.43437 9.60625L11.4375 5.05312C11.6031 4.675 11.4313 4.23125 11.0531 4.06563C10.675 3.9 10.2312 4.07187 10.0656 4.45L8.05937 9C8.04062 9 8.01875 9 8 9C6.89687 9 6 9.89688 6 11C6 12.1031 6.89687 13 8 13ZM5.5 4.5C5.5 4.23478 5.39464 3.98043 5.20711 3.79289C5.01957 3.60536 4.76522 3.5 4.5 3.5C4.23478 3.5 3.98043 3.60536 3.79289 3.79289C3.60536 3.98043 3.5 4.23478 3.5 4.5C3.5 4.76522 3.60536 5.01957 3.79289 5.20711C3.98043 5.39464 4.23478 5.5 4.5 5.5C4.76522 5.5 5.01957 5.39464 5.20711 5.20711C5.39464 5.01957 5.5 4.76522 5.5 4.5Z" fill="#EAEAEA"/>
            </g>
            <defs>
              <clipPath id="clip0_1_542">
                <path d="M0 0H16V16H0V0Z" fill="white"/>
              </clipPath>
            </defs>
          </svg>
          Dashboard
        </button>

        <button
          onClick={() => navigate("/member/referrals")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/referrals")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-5 w-5" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_481)">
              <path d="M3 4C3 2.93913 3.42143 1.92172 4.17157 1.17157C4.92172 0.421427 5.93913 0 7 0C8.06087 0 9.07828 0.421427 9.82843 1.17157C10.5786 1.92172 11 2.93913 11 4C11 5.06087 10.5786 6.07828 9.82843 6.82843C9.07828 7.57857 8.06087 8 7 8C5.93913 8 4.92172 7.57857 4.17157 6.82843C3.42143 6.07828 3 5.06087 3 4ZM0 15.0719C0 11.9937 2.49375 9.5 5.57188 9.5H8.42813C11.5063 9.5 14 11.9937 14 15.0719C14 15.5844 13.5844 16 13.0719 16H0.928125C0.415625 16 0 15.5844 0 15.0719ZM19.0406 16H14.7312C14.9 15.7063 15 15.3656 15 15V14.75C15 12.8531 14.1531 11.15 12.8188 10.0063C12.8938 10.0031 12.9656 10 13.0406 10H14.9594C17.7437 10 20 12.2562 20 15.0406C20 15.5719 19.5688 16 19.0406 16ZM13.5 8C12.5312 8 11.6562 7.60625 11.0219 6.97188C11.6375 6.14062 12 5.1125 12 4C12 3.1625 11.7938 2.37188 11.4281 1.67813C12.0094 1.25312 12.725 1 13.5 1C15.4344 1 17 2.56562 17 4.5C17 6.43437 15.4344 8 13.5 8Z" fill="#EAEAEA"/>
            </g>
            <defs>
              <clipPath id="clip0_1_481">
                <path d="M0 0H20V16H0V0Z" fill="white"/>
              </clipPath>
            </defs>
          </svg>
          Opportunities
        </button>

        <button
          onClick={() => navigate("/member/communities")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/communities")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-5 w-5" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_486)">
              <path d="M4.5 0C5.16304 0 5.79893 0.263392 6.26777 0.732233C6.73661 1.20107 7 1.83696 7 2.5C7 3.16304 6.73661 3.79893 6.26777 4.26777C5.79893 4.73661 5.16304 5 4.5 5C3.83696 5 3.20107 4.73661 2.73223 4.26777C2.26339 3.79893 2 3.16304 2 2.5C2 1.83696 2.26339 1.20107 2.73223 0.732233C3.20107 0.263392 3.83696 0 4.5 0ZM16 0C16.663 0 17.2989 0.263392 17.7678 0.732233C18.2366 1.20107 18.5 1.83696 18.5 2.5C18.5 3.16304 18.2366 3.79893 17.7678 4.26777C17.2989 4.73661 16.663 5 16 5C15.337 5 14.7011 4.73661 14.2322 4.26777C13.7634 3.79893 13.5 3.16304 13.5 2.5C13.5 1.83696 13.7634 1.20107 14.2322 0.732233C14.7011 0.263392 15.337 0 16 0ZM0 9.33438C0 7.49375 1.49375 6 3.33437 6H4.66875C5.16562 6 5.6375 6.10938 6.0625 6.30312C6.02187 6.52812 6.00313 6.7625 6.00313 7C6.00313 8.19375 6.52812 9.26562 7.35625 10C7.35 10 7.34375 10 7.33437 10H0.665625C0.3 10 0 9.7 0 9.33438ZM12.6656 10C12.6594 10 12.6531 10 12.6438 10C13.475 9.26562 13.9969 8.19375 13.9969 7C13.9969 6.7625 13.975 6.53125 13.9375 6.30312C14.3625 6.10625 14.8344 6 15.3313 6H16.6656C18.5063 6 20 7.49375 20 9.33438C20 9.70312 19.7 10 19.3344 10H12.6656ZM7 7C7 6.20435 7.31607 5.44129 7.87868 4.87868C8.44129 4.31607 9.20435 4 10 4C10.7956 4 11.5587 4.31607 12.1213 4.87868C12.6839 5.44129 13 6.20435 13 7C13 7.79565 12.6839 8.55871 12.1213 9.12132C11.5587 9.68393 10.7956 10 10 10C9.20435 10 8.44129 9.68393 7.87868 9.12132C7.31607 8.55871 7 7.79565 7 7ZM4 15.1656C4 12.8656 5.86562 11 8.16562 11H11.8344C14.1344 11 16 12.8656 16 15.1656C16 15.625 15.6281 16 15.1656 16H4.83437C4.375 16 4 15.6281 4 15.1656Z" fill="#EAEAEA"/>
            </g>
          </svg>
          Communities
        </button>

        <button
          onClick={() => navigate("/member/chats")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/chat")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-5 w-5" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_491)">
              <path d="M6.49998 11C10.0906 11 13 8.5375 13 5.5C13 2.4625 10.0906 0 6.49998 0C2.90935 0 -2.41971e-05 2.4625 -2.41971e-05 5.5C-2.41971e-05 6.70625 0.459351 7.82187 1.23748 8.73125C1.1281 9.025 0.965601 9.28438 0.793726 9.50313C0.643726 9.69688 0.490601 9.84688 0.378101 9.95C0.321851 10 0.274976 10.0406 0.243726 10.0656C0.228101 10.0781 0.215601 10.0875 0.209351 10.0906L0.203101 10.0969C0.0312258 10.225 -0.0437742 10.45 0.0249758 10.6531C0.0937258 10.8562 0.284351 11 0.499976 11C1.18123 11 1.86873 10.825 2.4406 10.6094C2.7281 10.5 2.99685 10.3781 3.23123 10.2531C4.1906 10.7281 5.30623 11 6.49998 11ZM14 5.5C14 9.00937 10.9031 11.6531 7.23435 11.9688C7.99373 14.2937 10.5125 16 13.5 16C14.6937 16 15.8094 15.7281 16.7719 15.2531C17.0062 15.3781 17.2719 15.5 17.5594 15.6094C18.1312 15.825 18.8187 16 19.5 16C19.7156 16 19.9094 15.8594 19.975 15.6531C20.0406 15.4469 19.9687 15.2219 19.7937 15.0938L19.7875 15.0875C19.7812 15.0812 19.7687 15.075 19.7531 15.0625C19.7218 15.0375 19.675 15 19.6187 14.9469C19.5062 14.8438 19.3531 14.6938 19.2031 14.5C19.0312 14.2812 18.8687 14.0187 18.7593 13.7281C19.5375 12.8219 19.9969 11.7063 19.9969 10.4969C19.9969 7.59687 17.3437 5.21875 13.9781 5.0125C13.9906 5.17188 13.9969 5.33437 13.9969 5.49687L14 5.5Z" fill="#EAEAEA"/>
            </g>
          </svg>
          Chats
        </button>

        <button
          onClick={() => navigate("/member/meetings")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/meetings")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_496)">
              <path d="M3 1V2H1.5C0.671875 2 0 2.67188 0 3.5V5H14V3.5C14 2.67188 13.3281 2 12.5 2H11V1C11 0.446875 10.5531 0 10 0C9.44687 0 9 0.446875 9 1V2H5V1C5 0.446875 4.55312 0 4 0C3.44688 0 3 0.446875 3 1ZM14 6H0V14.5C0 15.3281 0.671875 16 1.5 16H12.5C13.3281 16 14 15.3281 14 14.5V6Z" fill="#EAEAEA"/>
            </g>
          </svg>
          Meetings
        </button>

        <button
          onClick={() => navigate("/member/recommendations")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/recommendations")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 0C3.58 0 0 3.58 0 8C0 12.42 3.58 16 8 16C12.42 16 16 12.42 16 8C16 3.58 12.42 0 8 0ZM8 14C4.69 14 2 11.31 2 8C2 4.69 4.69 2 8 2C11.31 2 14 4.69 14 8C14 11.31 11.31 14 8 14ZM11.5 6L7 10.5L4.5 8L3.5 9L7 12.5L12.5 7L11.5 6Z" fill="#EAEAEA"/>
          </svg>
          Recommendations
        </button>

        <button
          onClick={() => navigate("/member/payment/dashboard")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/payment")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-[18px]" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 1C0.896875 1 0 1.89688 0 3V4H18V3C18 1.89688 17.1031 1 16 1H2ZM18 7H0V13C0 14.1031 0.896875 15 2 15H16C17.1031 15 18 14.1031 18 13V7ZM3.5 11H5.5C5.775 11 6 11.225 6 11.5C6 11.775 5.775 12 5.5 12H3.5C3.225 12 3 11.775 3 11.5C3 11.225 3.225 11 3.5 11ZM7 11.5C7 11.225 7.225 11 7.5 11H11.5C11.775 11 12 11.225 12 11.5C12 11.775 11.775 12 11.5 12H7.5C7.225 12 7 11.775 7 11.5Z" fill="#EAEAEA"/>
          </svg>
          Payments
        </button>

        <button
          onClick={() => navigate("/member/integrations")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/integrations")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_506)">
              <path d="M6 3.275C6 2.9875 5.81875 2.73438 5.5875 2.5625C5.225 2.29063 5 1.91562 5 1.5C5 0.671875 5.89687 0 7 0C8.10312 0 9 0.671875 9 1.5C9 1.91562 8.775 2.29063 8.4125 2.5625C8.18125 2.73438 8 2.9875 8 3.275C8 3.675 8.325 4 8.725 4H10.5C11.3281 4 12 4.67188 12 5.5V7.275C12 7.675 12.325 8 12.725 8C13.0125 8 13.2656 7.81875 13.4375 7.5875C13.7094 7.225 14.0844 7 14.5 7C15.3281 7 16 7.89687 16 9C16 10.1031 15.3281 11 14.5 11C14.0844 11 13.7094 10.775 13.4375 10.4125C13.2656 10.1813 13.0125 10 12.725 10C12.325 10 12 10.325 12 10.725V14.5C12 15.3281 11.3281 16 10.5 16H8.725C8.325 16 8 15.675 8 15.275C8 14.9875 8.18125 14.7344 8.4125 14.5625C8.775 14.2906 9 13.9156 9 13.5C9 12.6719 8.10312 12 7 12C5.89687 12 5 12.6719 5 13.5C5 13.9156 5.225 14.2906 5.5875 14.5625C5.81875 14.7344 6 14.9875 6 15.275C6 15.675 5.675 16 5.275 16H1.5C0.671875 16 0 15.3281 0 14.5V10.725C0 10.325 0.325 10 0.725 10C1.0125 10 1.26562 10.1813 1.4375 10.4125C1.70938 10.775 2.08437 11 2.5 11C3.32812 11 4 10.1031 4 9C4 7.89687 3.32812 7 2.5 7C2.08437 7 1.70938 7.225 1.4375 7.5875C1.26562 7.81875 1.0125 8 0.725 8C0.325 8 0 7.675 0 7.275V5.5C0 4.67188 0.671875 4 1.5 4H5.275C5.675 4 6 3.675 6 3.275Z" fill="#EAEAEA"/>
            </g>
          </svg>
          Integrations
        </button>
        <button
          onClick={() => navigate("/member/plans")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/plans")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
<svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M2 2C2 1.44687 1.55313 1 1 1C0.446875 1 0 1.44687 0 2V12.5C0 13.8813 1.11875 15 2.5 15H15C15.5531 15 16 14.5531 16 14C16 13.4469 15.5531 13 15 13H2.5C2.225 13 2 12.775 2 12.5V2ZM14.7063 4.70625C15.0969 4.31563 15.0969 3.68125 14.7063 3.29063C14.3156 2.9 13.6812 2.9 13.2906 3.29063L10 6.58437L8.20625 4.79063C7.81563 4.4 7.18125 4.4 6.79063 4.79063L3.29063 8.29062C2.9 8.68125 2.9 9.31563 3.29063 9.70625C3.68125 10.0969 4.31563 10.0969 4.70625 9.70625L7.5 6.91563L9.29375 8.70938C9.68437 9.1 10.3188 9.1 10.7094 8.70938L14.7094 4.70937L14.7063 4.70625Z" fill="#EAEAEA"/>
</svg>
          Plans
        </button>

        <button
          onClick={() => navigate("/member/notes")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/notes")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 12h2V7h-2v5zm-4 0h2V7H5v5zm8-10h-1V0h-2v2H6V0H4v2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 14H3V7h10v9z" fill="#EAEAEA"/>
          </svg>
          Notes and Tasks
        </button>

        <button
          onClick={() => navigate("/member/affiliate")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/affiliate")
                  ? "bg-[#2e7d text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
<svg className="h-4 w-[18px]" viewBox="0 0 18 16" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M10.1062 2.6625L7.08125 5.1125C6.57812 5.51875 6.48125 6.25 6.8625 6.77187C7.26562 7.32812 8.05 7.4375 8.59062 7.01562L11.6938 4.60313C11.9125 4.43438 12.225 4.47188 12.3969 4.69063C12.5688 4.90938 12.5281 5.22188 12.3094 5.39375L11.6562 5.9L16 9.9V4H15.9781L15.8562 3.92188L13.5875 2.46875C13.1094 2.1625 12.55 2 11.9812 2C11.3 2 10.6375 2.23437 10.1062 2.6625ZM10.8188 6.55L9.20312 7.80625C8.21875 8.575 6.79063 8.375 6.05312 7.3625C5.35938 6.40938 5.53437 5.07812 6.45 4.3375L9.05 2.23438C8.6875 2.08125 8.29688 2.00312 7.9 2.00312C7.3125 2 6.74062 2.175 6.25 2.5L4 4V11H4.88125L7.7375 13.6062C8.35 14.1656 9.29688 14.1219 9.85625 13.5094C10.0281 13.3187 10.1438 13.0969 10.2031 12.8656L10.7344 13.3531C11.3438 13.9125 12.2937 13.8719 12.8531 13.2625C12.9937 13.1094 13.0969 12.9312 13.1625 12.7469C13.7688 13.1531 14.5938 13.0687 15.1031 12.5125C15.6625 11.9031 15.6219 10.9531 15.0125 10.3938L10.8188 6.55ZM0.5 4C0.225 4 0 4.225 0 4.5V11C0 11.5531 0.446875 12 1 12H2C2.55312 12 3 11.5531 3 11V4H0.5ZM1.5 10C1.63261 10 1.75979 10.0527 1.85355 10.1464C1.94732 10.2402 2 10.3674 2 10.5C2 10.6326 1.94732 10.7598 1.85355 10.8536C1.75979 10.9473 1.63261 11 1.5 11C1.36739 11 1.24021 10.9473 1.14645 10.8536C1.05268 10.7598 1 10.6326 1 10.5C1 10.3674 1.05268 10.2402 1.14645 10.1464C1.24021 10.0527 1.36739 10 1.5 10ZM17 4V11C17 11.5531 17.4469 12 18 12H19C19.5531 12 20 11.5531 20 11V4.5C20 4.225 19.775 4 19.5 4H17ZM18 10.5C18 10.3674 18.0527 10.2402 18.1464 10.1464C18.2402 10.0527 18.3674 10 18.5 10C18.6326 10 18.7598 10.0527 18.8536 10.1464C18.9473 10.2402 19 10.3674 19 10.5C19 10.6326 18.9473 10.7598 18.8536 10.8536C18.7598 10.9473 18.6326 11 18.5 11C18.3674 11 18.2402 10.9473 18.1464 10.8536C18.0527 10.7598 18 10.6326 18 10.5Z" fill="#EAEAEA"/>
</svg>
          Affiliate Program
        </button>
        <button
          onClick={() => navigate("/member/profile")}
          className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] ${
                location.pathname.startsWith("/member/profile")
                  ? "bg-[#2e7d32] text-[#eaeaea]"
                  : "text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]"
              }`}
        >
          <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M8 8C10.21 8 12 6.21 12 4C12 1.79 10.21 0 8 0C5.79 0 4 1.79 4 4C4 6.21 5.79 8 8 8ZM8 10C5.33 10 0 11.34 0 14V16H16V14C16 11.34 10.67 10 8 10Z" fill="#EAEAEA"/>
          </svg>
          My Profile
        </button>
      <button
        onClick={handleLogout}
        className={`flex w-full items-center gap-3 px-4 py-3 text-left text-[16px] text-[#b5b5b5] hover:bg-[#252525] hover:text-[#eaeaea]`}
      >
        <svg className="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M15.7063 8.70625C16.0969 8.31563 16.0969 7.68125 15.7063 7.29063L11.7063 3.29063C11.3156 2.9 10.6812 2.9 10.2906 3.29063C9.9 3.68125 9.9 4.31563 10.2906 4.70625L12.5844 7H6C5.44688 7 5 7.44688 5 8C5 8.55312 5.44688 9 6 9H12.5844L10.2906 11.2937C9.9 11.6844 9.9 12.3188 10.2906 12.7094C10.6812 13.1 11.3156 13.1 11.7063 12.7094L15.7063 8.70938V8.70625ZM5 3C5.55312 3 6 2.55313 6 2C6 1.44687 5.55312 1 5 1H3C1.34375 1 0 2.34375 0 4V12C0 13.6562 1.34375 15 3 15H5C5.55312 15 6 14.5531 6 14C6 13.4469 5.55312 13 5 13H3C2.44688 13 2 12.5531 2 12V4C2 3.44688 2.44688 3 3 3H5Z" fill="#EAEAEA"/>
        </svg>
        Logout
      </button>

      </div>

    </div>
  );
};

export default Sidebar;