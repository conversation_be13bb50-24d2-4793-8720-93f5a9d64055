import{j as e}from"./@react-google-maps/api-211df1ae.js";import{R as i,f as p,d as N}from"./vendor-1c28ea83.js";import{M as f,A as u,G as g,t as b,S as v}from"./index-b1726834.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./react-hook-form-eec8b32f.js";import"./@hookform/resolvers-2530002b.js";import"./yup-493dd417.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";let l=new f;const H=()=>{const{dispatch:m}=i.useContext(u);i.useContext(g);const[s,x]=i.useState({}),[h,r]=i.useState(!0),c=p(),d=N(),a=t=>t?new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"N/A";i.useEffect(function(){(async function(){try{r(!0),l.setTable("referral");const t=await l.callRestAPI({id:Number(c==null?void 0:c.id)},"GET");t.error||(x(t.model),r(!1))}catch(t){r(!1),console.log("error",t),b(m,t.message)}})()},[]);const o=t=>{switch(t==null?void 0:t.toLowerCase()){case"active":return"status active";case"completed":return"status completed";case"deleted":return"status deleted";default:return"status"}},n=t=>({full_time:"Full Time",part_time:"Part Time",contract:"Contract",freelance:"Contract",internship:"Part Time"})[t]||t;return e.jsx("div",{className:"view-referral-page bg-[#1E1E1E] text-white min-h-screen",children:e.jsx("div",{className:"container mx-auto p-6",children:h?e.jsx("div",{className:"bg-[#161616] p-6 rounded",children:e.jsx(v,{})}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"header mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h1",{className:"text-2xl font-semibold",children:"Opportunity Details"}),e.jsxs("button",{onClick:()=>d("/admin/referral"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]}),e.jsx("p",{className:"text-[#9ca3af] text-sm",children:"View and manage opportunity details"})]}),e.jsxs("div",{className:"content-wrapper bg-[#161616] rounded-lg border border-[#2d2d3d] overflow-hidden",children:[e.jsx("div",{className:"header-section p-6 border-b border-[#2d2d3d]",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-semibold mb-2",children:(s==null?void 0:s.title)||"Untitled Opportunity"}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-[#9ca3af]",children:[e.jsxs("div",{children:["ID: ",s==null?void 0:s.id]}),e.jsxs("div",{children:["Posted: ",a(s==null?void 0:s.created_at)]}),(s==null?void 0:s.expiration_date)&&e.jsxs("div",{children:["Expires: ",a(s==null?void 0:s.expiration_date)]})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:o(s==null?void 0:s.status),children:(s==null?void 0:s.status)||"Unknown"}),e.jsx("span",{className:"type-badge bg-[#252538] text-white px-3 py-1 rounded-full text-xs",children:n(s==null?void 0:s.type)})]})]})}),e.jsxs("div",{className:"details-grid p-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"left-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Basic Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[(s==null?void 0:s.job_title)&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Job Title"}),e.jsx("div",{children:s.job_title})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Title"}),e.jsx("div",{children:(s==null?void 0:s.title)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Industry ID"}),e.jsx("div",{children:(s==null?void 0:s.industry_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Type"}),e.jsx("div",{children:n(s==null?void 0:s.type)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Status"}),e.jsx("div",{children:(s==null?void 0:s.status)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Is Active"}),e.jsx("div",{children:s!=null&&s.is_active?"Yes":"No"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Know Client"}),e.jsx("div",{children:(s==null?void 0:s.know_client)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Referral Type"}),e.jsx("div",{children:(s==null?void 0:s.referral_type)||"N/A"})]})]})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Financial Details"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Pay"}),e.jsx("div",{children:s!=null&&s.pay?`$${s.pay}`:"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Deal Size"}),e.jsx("div",{children:s!=null&&s.deal_size?`$${s.deal_size}`:"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Payment Method"}),e.jsx("div",{children:(s==null?void 0:s.payment_method)||"N/A"})]})]})]})]}),e.jsxs("div",{className:"right-column space-y-6",children:[e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Description"}),e.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4",children:e.jsx("p",{className:"whitespace-pre-wrap",children:(s==null?void 0:s.description)||"No description provided."})})]}),(s==null?void 0:s.additional_notes)&&e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Additional Notes"}),e.jsx("div",{className:"bg-[#1a1a1a] rounded-lg p-4",children:e.jsx("p",{className:"whitespace-pre-wrap",children:s.additional_notes})})]}),e.jsxs("div",{className:"detail-section",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Referral Information"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 space-y-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"User ID"}),e.jsx("div",{children:(s==null?void 0:s.user_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Referred To ID"}),e.jsx("div",{children:(s==null?void 0:s.referred_to_id)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Reposted From"}),e.jsx("div",{children:(s==null?void 0:s.reposted_from)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Completed By"}),e.jsx("div",{children:(s==null?void 0:s.completed_by)||"N/A"})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Community ID"}),e.jsx("div",{children:(s==null?void 0:s.community_id)||"N/A"})]})]})]})]})]}),e.jsxs("div",{className:"dates-section p-6 border-t border-[#2d2d3d]",children:[e.jsx("h3",{className:"text-[#9ca3af] text-sm font-medium mb-2",children:"Dates"}),e.jsxs("div",{className:"bg-[#1a1a1a] rounded-lg p-4 grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Created At"}),e.jsx("div",{children:a(s==null?void 0:s.created_at)})]}),e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Updated At"}),e.jsx("div",{children:a(s==null?void 0:s.updated_at)})]}),(s==null?void 0:s.expiration_date)&&e.jsxs("div",{className:"detail-item",children:[e.jsx("div",{className:"text-[#9ca3af] text-sm",children:"Expiration Date"}),e.jsx("div",{children:a(s==null?void 0:s.expiration_date)})]})]})]}),e.jsxs("div",{className:"actions-section p-6 border-t border-[#2d2d3d] flex justify-end gap-4",children:[e.jsxs("button",{onClick:()=>d(`/admin/edit-referral/${s==null?void 0:s.id}`),className:"edit-button bg-[#22c55e] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Edit Opportunity"]}),e.jsxs("button",{onClick:()=>d("/admin/referral"),className:"back-button bg-[#161616] text-white px-4 py-2 rounded flex items-center",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"mr-2",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to List"]})]})]})]})})})};export{H as default};
