import{j as m}from"./@react-google-maps/api-211df1ae.js";import{r as f,R as d}from"./vendor-1c28ea83.js";import{u as we}from"./react-hook-form-eec8b32f.js";import{af as ye,a3 as Se,M as Ne,A as je,G as Te,ag as Pe,ah as Ee,a7 as $e,t as Os,ai as ms,aj as ke,L as As,ak as ve,a as Re,o as Me}from"./index-b1726834.js";import{c as Le}from"./yup-493dd417.js";import{P as Oe}from"./index-255f40d5.js";import{c as Ae,T as _e,d as ze,O as Ue}from"./index-23b1bf97.js";import{A as Be}from"./index-e2604cb4.js";import{b as Ge}from"./index.esm-4be700bd.js";import{M as Ie}from"./MkdInput-a6188ffc.js";import{getProcessedTableData as Je}from"./MkdListTableRowListColumn-83b9585f.js";import"./react-confirm-alert-143e7b1b.js";/* empty css                          *//* empty css                            */import"./qr-scanner-cf010ec4.js";import"./pdf-lib-623decea.js";import"./@headlessui/react-0d33a5d7.js";import"./react-quill-4ec0fa7c.js";import"./@craftjs/core-a5d68af1.js";import"./@hookform/resolvers-2530002b.js";import"./react-icons-5238c8a8.js";import"./country-state-city-7cb9a309.js";import"./@stripe/stripe-js-6b714a86.js";import"./@stripe/react-stripe-js-69862bfd.js";import"./@fortawesome/react-fontawesome-b5aa048d.js";import"./react-pdf-d6d1c22a.js";import"./@react-pdf-viewer/core-75a73120.js";import"./react-calendar-eace60fa.js";import"./react-toggle-58b0879a.js";import"./@uppy/dashboard-3a4b1704.js";/* empty css                 */import"./index-dbfe2d0c.js";const Ke=({onClick:$,className:N,showIcon:hs=!0,showText:O=!0})=>m.jsx(m.Fragment,{children:m.jsxs("button",{type:"button",onClick:$,className:`relative flex h-[3rem] w-fit min-w-fit items-center  justify-center gap-2 overflow-hidden rounded-[.625rem] border border-primary bg-primary px-[.625rem]  py-[.5625rem] font-inter text-sm font-medium leading-none text-white ${N}`,children:[hs&&m.jsx(ye,{}),O&&m.jsx("span",{children:"Export"})]})});const Qe=($,N)=>typeof $=="string"&&["eq"].includes(N)?`'${$}'`:$,ps=($,N)=>typeof N=="number"&&["cs"].includes($)?"eq":$,We=({pageUser:$=!1,columns:N=[],useDefaultColumns:hs=!1,defaultColumns:O=[],excludeColumns:gs=[],columnModel:ds=null,processes:Q=[],searchInitialProcesses:bs=[],actions:n={view:{show:!0,multiple:!0,action:null},edit:{show:!0,multiple:!0,action:null},delete:{show:!0,multiple:!0,action:null},select:{show:!0,multiple:!0,action:null},action:{show:!1,multiple:!1,action:null,showChildren:!0,children:"+ Add",type:"",className:"",locations:[],icon:null},add:{show:!0,multiple:!0,action:null,showChildren:!0,children:"+ Add",type:"",className:""},export:{show:!0,multiple:!0,action:null,showText:!1,className:""}},updateRef:Cs=null,onUpdateCurrentTableData:ws=null,actionPostion:_s=["dropdown"],actionPosition:zs,actionId:Us="id",tableRole:Bs="admin",table:w="user",tableTitle:B="",tableSchema:Xe=[],hasFilter:W=!0,schemaFields:Ye=[],showPagination:Gs=!0,defaultFilter:v=[],refreshRef:ys=null,allowEditing:Is=!1,allowSortColumns:Js=!0,showSearch:Ks=!0,topClasses:Qs="",join:Ze=[],filterDisplays:Ws=[],resetFilters:X=null,defaultPageSize:Ss=500,searchFilter:R=[],searchField:a=null,onReady:Ns=null,maxHeight:D=null,rawFilter:Xs=[],externalData:e={page:1,data:[],limit:0,pages:0,total:0,use:!1,loading:!1,canNextPage:!1,canPreviousPage:!1,fetch:(Y,M,k)=>{},search:(Y,M,k,L)=>{}},canChangeLimit:Ys=!0,selectedItemsRef:js=null})=>{var vs,Rs,Ms;const Y=f.useMemo(()=>new Se,[]),M=f.useMemo(()=>new Ne,[]);f.useRef(null);const{dispatch:k}=d.useContext(je),{dispatch:L,state:{columModel:ss}}=d.useContext(Te);d.useState("");const[G,_]=d.useState([]),[t,Z]=d.useState(Ss),[j,q]=d.useState(0),[qe,F]=d.useState(0),[c,I]=d.useState(1),[Zs,H]=d.useState(!1),[qs,V]=d.useState(!1),[Fs,es]=d.useState(!1),[Hs,os]=d.useState(!1),[is,rs]=d.useState([]),[P,cs]=d.useState([]),[S,ls]=d.useState([]),[p,ts]=d.useState(""),[z,U]=d.useState(!1);d.useState(!1);const[Vs,b]=d.useState(!1),[Fe,xs]=d.useState(!1),Ts=s=>({referral:"title",community:"title",user:"email",payment:"type",plan:"name"})[s]||"name",[ns,Ps]=d.useState(a||Ts(w));d.useEffect(()=>{Ps(a||Ts(w))},[w,a]);const[o,A]=d.useState({views:[],data:null,columns:[],columnId:0,columnsReady:!1}),[as,Ds]=d.useState(!1),Es={id:22};console.log("somethinng",Es);const J=f.useMemo(()=>is,[is]),se=f.useMemo(()=>JSON.stringify(G),[G]),ee=Le({}),{register:He,handleSubmit:oe,setError:Ve,reset:$s,formState:{errors:xe}}=we({resolver:Me(ee)}),ie=f.useCallback(()=>{let s=[];return new Set(J.map(r=>r==null?void 0:r.accessor)).forEach(r=>{const l=J.filter(u=>u.accessor===r);if((l==null?void 0:l.length)>0){const u=l.filter(h=>h==null?void 0:h.value);if(u.length>1)u.forEach(h=>{const{accessor:C,operator:g,value:T}=h,K=`Dealmaker_${w}.${C},${ps(g==="cs"||g==="eq"?"o"+g:g,T)},${T}`;s.push(K)});else if(u.length===1){const{accessor:h,operator:C,value:g}=u[0];s.push(`Dealmaker_${w}.${h},${ps(C,g)},${Qe(g,C)}`)}}}),s},[J,w]),E=f.useCallback(async(s={limit:t,page:1})=>{const i=ie();let r=o==null?void 0:o.columns;if(bs.length>0)for(const l of bs)r=l(r,i);console.log("treeFilter >>",i,r);try{const l=`/v3/api/custom/Dealmaker/generic/search/${w}?limit=${s==null?void 0:s.limit}&page=${s==null?void 0:s.page}`;b(!0);const u=await Pe(L,k,{endpoint:l,method:"POST",payload:{search:p,columns:r,filter:R,tree_filter:i}},"tableSearchData",!1);if(!(u!=null&&u.error)){ls([]);const{data:h,total:C,limit:g,num_pages:T,page:K}=u;let fs=h;if(Q!=null&&Q.length)for(const Ls of Q)["function"].includes(typeof Ls)&&(fs=Ls(fs,o==null?void 0:o.columns));const Ce=await Je(fs,o==null?void 0:o.columns,L,k);_(()=>Ce),Z(Number(g)),q(T??j),I(Number(K)),F(Number(C)),H(Number(K)>1),V(Number(K)+1<=T?Number(T):j),b(!1)}b(!1)}catch{b(!1)}},[w,t,p,o,R,L,k,j,J]),y=f.useCallback(async(s,i,r)=>{console.log("getData called with:",{pageNum:s,limitNum:i,filterConditions:P,table:w});try{b(!0);const l=await Y.getPaginate("super_admin",w,{size:i||500,page:s||1,...P.length?{filter:[...v.length?v:[],...P]}:v.length?{filter:[...v]}:null}),{list:u,total:h,limit:C,num_pages:g,page:T}=l;_(u),Z(Number(C)),q(g??j),I(Number(T)),F(Number(h)),H(Number(T)>1),V(Number(T)+1<=g?Number(g):j),b(!1)}catch(l){b(!1),console.error("ERROR fetching data:",l)}},[w,v,j]),re=f.useCallback(s=>{o!=null&&o.columns[s].isSorted?o.columns[s].isSortedDesc=!(o!=null&&o.columns[s].isSortedDesc):(o==null||o.columns.forEach(i=>{i.isSorted=!1,i.isSortedDesc=!1}),o.columns[s].isSorted=!0),async function(){p?p&&E({limit:t,page:c}):e!=null&&e.use?(b(!0),e==null||e.fetch(c,t,{filterConditions:[],order:o==null?void 0:o.columns[s].accessor,direction:o!=null&&o.columns[s].isSortedDesc?"desc":"asc"})):await y(c,t,{filterConditions:[],order:o==null?void 0:o.columns[s].accessor,direction:o!=null&&o.columns[s].isSortedDesc?"desc":"asc"})}()},[o,c,t,P,y]),ce=f.useCallback(s=>{(async function(){Z(s),p?p&&E({limit:s,page:c}):(await y(c,s,{filterConditions:[]}),U(!1))})()},[z,p,c,y,E]),le=f.useCallback(s=>{const i={uid:Ee(),accessor:s,operator:"cs",value:""};rs(r=>[...r,i])},[]),te=f.useCallback((s,i,r)=>{rs(l=>l.map(u=>(u==null?void 0:u.uid)===r?{...u,[s]:i}:u)),s==="value"&&xs(!0)},[]),ne=f.useCallback(()=>{(async function(){p?p&&(e!=null&&e.use?e==null||e.search(p,N,R,{limit:t,page:c-1>0?c-1:c}):E({limit:t,page:c-1>0?c-1:c})):(e!=null&&e.use?(b(!0),e==null||e.fetch(c-1>0?c-1:c,t)):await y(c-1>0?c-1:c,t,{filterConditions:[]}),U(!1))})()},[z,p,c,t,y,E]),ue=f.useCallback(s=>{(async function(){I(s),p?p&&(e!=null&&e.use?e==null||e.search(p,N,R,{limit:t,page:s}):E({limit:t,page:s})):(e!=null&&e.use?(b(!0),e==null||e.fetch(s,t)):await y(s,t,{filterConditions:[]}),U(!1))})()},[z,p,t,y,E]),fe=f.useCallback(()=>{(async function(){p?p&&(e!=null&&e.use?e==null||e.search(p,N,R,{limit:t,page:c+1<=j?c+1:c}):E({limit:t,page:c+1<=j?c+1:c})):(e!=null&&e.use?(b(!0),e==null||e.fetch(c+1<=j?c+1:c,t)):await y(c+1<=j?c+1:c,t,{filterConditions:[]}),U(!1))})()},[z,p,c,j,t,y,E]),me=f.useCallback((s,i,r)=>{if(!r||r.trim()===""){cs(h=>h.filter(C=>!C.includes(s))),ts("");return}const l=i==="eq"&&isNaN(r)?`${r}`:r,u=`${s},${i},${l}`.toLowerCase();cs(h=>[...h.filter(g=>!g.includes(s)),u]),ts(r)},[]),pe=f.useCallback(async s=>{const i=async r=>{try{os(!0),M.setTable(w);const l=await M.callRestAPI({id:r},"DELETE");l!=null&&l.error||(_(u=>u.filter(h=>Number(h.id)!==Number(r))),os(!1),es(!1))}catch(l){throw os(!1),es(!1),Os(k,l==null?void 0:l.message),new Error(l)}};if(Array.isArray(s))for(const r of s)await i(r);else typeof s=="number"&&await i(s)},[w,k]),he=f.useCallback(async()=>{try{M.setTable(w);const s={search:$e(p),columns:o==null?void 0:o.columns,exclude_columns:gs,filter:R,raw_filter:Xs};await M.customExportCSV(s)}catch(s){throw new Error(s)}},[w,p,o,gs,R]);f.useCallback(async s=>{var i,r,l;s==null||s.preventDefault(),[(i=s==null?void 0:s.code)==null?void 0:i.toLowerCase(),(r=s==null?void 0:s.key)==null?void 0:r.toLowerCase()].includes("enter")?p?p&&(e!=null&&e.use?e==null||e.search(p,N,R):E({limit:t,page:c})):(e!=null&&e.use?(b(!0),e==null||e.fetch(c,t,{filterConditions:[]})):await y(c,t,{filterConditions:[]}),U(!1)):(ts((l=s==null?void 0:s.target)==null?void 0:l.value),z||U(!0))},[z,p,c,t,y,E]),f.useCallback(async()=>{$s(),await y(c,t)},[$s,y,c,t]);const ge=f.useCallback(()=>{p?p&&(e!=null&&e.use?(b(!0),e==null||e.search(c,t,{filterConditions:[]})):E({limit:t,page:c})):e!=null&&e.use?(b(!0),e==null||e.fetch(c,t,{filterConditions:[]})):y(c,t,{filterConditions:[]})},[J,w,y,c,t]),ks=f.useCallback(async(s,i,r)=>{try{M.setTable(w),await M.callRestAPI({id:s,[i]:r},"PUT")}catch(l){console.log("ERROR",l),Os(k,l.message)}},[w,k]),de=f.useCallback(async(s,i,r,l)=>{let u;i=isNaN(Number.parseInt(i))?i:Number.parseInt(i);try{clearTimeout(u),u=setTimeout(async()=>{await ks(s,l,i)},200),_(h=>h.map((C,g)=>g===r?{...C,[l]:i}:C))}catch(h){console.error(h)}},[ks]),us=f.useCallback((s,i=[])=>{if(!s)return A(l=>({...l,columns:[...O],columnsReady:!0,views:i}));const r=s!=null&&s.columns?JSON.parse(s==null?void 0:s.columns):[];A(l=>({...l,data:s,views:i,columnId:i!=null&&i.length?s==null?void 0:s.column_id:s==null?void 0:s.id,columnsReady:!0,columns:r!=null&&r.length?r:O}))},[O,o]);f.useCallback(async()=>{try{A(s=>({...s,columnsReady:!1})),ms(L,!0,"columModel"),console.log("Using default columns to avoid API call failures"),us(null,[]),ms(L,!1,"columModel")}catch(s){console.error("Error in getColumns:",s),us(null,[]),ms(L,!1,"columModel")}},[ds,w,Es,L,k,us,A]);const be=f.useCallback(s=>{Z(s==null?void 0:s.limit),q(s==null?void 0:s.pages),I(s==null?void 0:s.page),F(s==null?void 0:s.total),H((s==null?void 0:s.page)>1),V((s==null?void 0:s.page)+1<=(s==null?void 0:s.pages))},[]);d.useEffect(()=>{var s;(s=n==null?void 0:n.select)!=null&&s.action&&n.select.action(S)},[S==null?void 0:S.length]),d.useEffect(()=>{Ns&&Ns(G)},[se]),f.useEffect(()=>{const s=o==null?void 0:o.columns.find(i=>i==null?void 0:i.searchable);s&&Ps(s==null?void 0:s.accessor)},[]),d.useEffect(()=>{const s=(N==null?void 0:N.length)>0?N:O;A({columns:[...s],columnsReady:!0,views:[]})},[]),f.useEffect(()=>{if(o!=null&&o.columnsReady&&P.length===0)try{y(c,t,{filterConditions:[]})}catch{b(!1)}},[o==null?void 0:o.columnsReady]),f.useEffect(()=>{X&&(e!=null&&e.use?(b(!0),e==null||e.fetch(1,t,{filterConditions:X})):y(1,t,{filterConditions:X}))},[X]),f.useEffect(()=>{if(o!=null&&o.columnsReady&&P.length>0){const s=setTimeout(()=>{(async()=>{console.log("Search triggered with filterConditions:",P);try{b(!0);const r=await Y.getPaginate("super_admin",w,{size:t||500,page:1,...P.length?{filter:[...v.length?v:[],...P]}:v.length?{filter:[...v]}:null}),{list:l,total:u,limit:h,num_pages:C,page:g}=r;_(l),q(C??j),I(Number(g)),F(Number(u)),H(Number(g)>1),V(Number(g)+1<=C?Number(C):j),b(!1)}catch(r){b(!1),console.error("ERROR fetching search data:",r)}})()},500);return()=>clearTimeout(s)}},[P]),f.useEffect(()=>{if(o!=null&&o.columnsReady&&P.length===0&&p===""){const s=setTimeout(()=>{y(1,t,{filterConditions:[]})},100);return()=>clearTimeout(s)}},[P,p]);const x=zs||_s;return console.log("Using action position:",x),m.jsxs("div",{className:`relative grid !h-full !max-h-full !min-h-full w-full min-w-full max-w-full items-start gap-2 ${D||"grid-rows-[auto_1fr_auto]"}`,children:[js&&m.jsx("button",{type:"button",ref:js,onClick:()=>{S!=null&&S.length&&ls([])},className:"hidden"}),Cs&&m.jsx("button",{type:"button",ref:Cs,onClick:()=>{ws&&ws(s=>{_(()=>s==null?void 0:s.data),be(s)}),b(!1)},className:"hidden"}),ys&&m.jsx("button",{type:"button",ref:ys,onClick:()=>{if(console.log("Refresh button clicked"),e!=null&&e.use){b(!0);try{e==null||e.fetch(c,t,{filterConditions:[]});const s=setTimeout(()=>{b(!1),console.log("MkdListTableV2 safety timeout: Loading state reset to false")},2e3);return()=>clearTimeout(s)}catch(s){console.error("Error in refresh:",s),b(!1)}}else try{y(1,t,{filterConditions:[]})}catch(s){console.error("Error in refresh:",s),b(!1)}},className:"hidden"}),m.jsxs("div",{className:`flex w-full justify-between ${B&&W?"flex-col gap-3":"h-fit items-center"} ${Qs}`,children:[m.jsx("h4",{className:"flex items-center font-inter text-[1rem] font-bold capitalize leading-[1.5rem] tracking-[-0.011em]",children:B||""}),m.jsxs("div",{className:`flex h-fit flex-col md:flex-row ${W?"w-full":"w-fit"} items-start justify-between gap-2 text-center md:items-center`,children:[W?m.jsx(Ae,{table:w,columnModel:ds||w,onSubmit:ge,columnData:o,searchField:ns,handleSubmit:oe,setColumnData:A,onColumnClick:le,filterDisplays:Ws,setOptionValue:te,selectedOptions:is,setSelectedOptions:rs,setFilterConditions:cs}):null,m.jsxs("div",{className:`flex h-full w-full justify-between gap-2 self-end md:w-fit md:flex-row md:justify-end ${!B&&!W?"w-full":""}`,children:[Object.keys(n).map((s,i)=>{var r,l;if(n[s].show&&n[s].hasOwnProperty("type")&&["toggle"].includes(n[s].type))return m.jsx(Ie,{type:"toggle",onChange:u=>{var h,C,g;(h=n[s])!=null&&h.action&&((g=n[s])==null||g.action((C=u==null?void 0:u.target)==null?void 0:C.checked))},label:((r=n[s])==null?void 0:r.children)??s,value:(l=n[s])==null?void 0:l.value},i)}),Ks?m.jsxs("div",{className:"flex cursor-pointer items-center justify-between gap-3 rounded-md border border-gray-200 px-2 py-1 focus-within:border-gray-400",children:[m.jsx(ke,{className:"text-xl text-gray-200"}),m.jsx("input",{type:"text",placeholder:`Search by ${ns}...`,className:"w-full border-none p-0 placeholder:text-left focus:outline-none bg-black",style:{boxShadow:"0 0 transparent"},onKeyDown:s=>{s.key==="Enter"&&s.preventDefault()},onInput:s=>{var i;return me(ns,"cs",(i=s.target)==null?void 0:i.value)}}),m.jsx(Ge,{className:"text-lg text-gray-200"})]}):null,S!=null&&S.length&&x.includes("ontop")?m.jsx(As,{children:m.jsx(_e,{actions:n,selectedItems:S})}):null,m.jsxs("div",{className:"flex w-[auto] items-center justify-end gap-2 self-end",children:[Object.keys(n).map((s,i)=>{var r,l,u,h,C;if(n[s].show&&n[s].hasOwnProperty("type")&&["static"].includes(n[s].type))return m.jsxs(Be,{onClick:()=>{var g,T;(g=n[s])!=null&&g.action&&((T=n[s])==null||T.action())},title:((r=n[s])==null?void 0:r.title)??s,showPlus:!1,className:`!h-[2.5rem] ${(l=n[s])==null?void 0:l.className}`,loading:((u=n[s])==null?void 0:u.loading)??!1,disabled:((h=n[s])==null?void 0:h.disabled)??!1,icon:((C=n[s])==null?void 0:C.icon)??null,children:[s==="delete"?m.jsx(ve,{}):null,n[s].children?n[s].children:m.jsx(m.Fragment,{children:Re(s==="delete"?"Remove":s,{casetype:"capitalize",separator:" "})})]},i)}),((vs=n==null?void 0:n.export)==null?void 0:vs.show)&&m.jsx(Ke,{showText:(Rs=n==null?void 0:n.export)==null?void 0:Rs.showText,onClick:he,className:`mx-1 !h-[2.5rem] ${(Ms=n==null?void 0:n.export)==null?void 0:Ms.className}`})]})]})]})]}),m.jsx(ze,{columns:N||O,maxHeight:D,table:w,onSort:re,actions:n,actionId:Us,tableRole:Bs,tableTitle:B,columnData:o,deleteItem:pe,allowEditing:Is,setColumnData:A,actionPostion:x,deleteLoading:Hs,selectedItems:S,showDeleteModal:Fs,allowSortColumns:Js,currentTableData:G,setSelectedItems:ls,setShowDeleteModal:es,setLoading:b,loading:Vs||(ss==null?void 0:ss.loading)||(e==null?void 0:e.loading),handleTableCellChange:de,onPopoverStateChange:Ds,popoverShown:as}),S!=null&&S.length&&x.includes("overlay")?m.jsx(As,{children:m.jsx(Ue,{actions:n,selectedItems:S,currentTableData:G})}):null,Gs?m.jsx(Oe,{currentPage:c,pageCount:j,pageSize:t,startSize:Ss,canPreviousPage:Zs,canNextPage:qs,updatePageSize:ce,previousPage:ne,nextPage:fe,updateCurrentPage:ue,canChangeLimit:Ys}):null]})},Lo=f.memo(We);export{Lo as default};
