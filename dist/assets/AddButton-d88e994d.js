import{j as e}from"./@react-google-maps/api-211df1ae.js";import{r as n}from"./vendor-1c28ea83.js";import{_ as h}from"./MoonLoader-ca436abf.js";const j="_button_d15sp_1",w={button:j},E=({onClick:t,children:a="Add New",showPlus:y=!0,className:i,showChildren:f=!0,loaderclasses:d,color:l="#ffffff",loading:r=!1,disabled:u=!1,icon:s=null,title:m,anination:c=!0})=>{const p={borderColor:"#ffffff"},x=n.useId(),[b,o]=n.useState(!1),g=()=>{t&&t(),c&&o(!0)};return e.jsx("button",{title:m,disabled:u,type:"button",onAnimationEnd:()=>o(!1),onClick:g,className:`${b&&"animate-wiggle"} ${w.button} relative flex h-[3rem] cursor-pointer  items-center justify-center gap-2 overflow-hidden rounded-[.625rem] border border-primary bg-primary px-[.625rem] py-2 font-inter text-sm font-medium leading-loose tracking-wide text-white ${i}`,children:e.jsxs(e.Fragment,{children:[e.jsx(h,{color:l,loading:r,cssOverride:p,size:14,className:d,"data-testid":x}),!r&&s?s:null,f?a:null]})})};export{E as default};
